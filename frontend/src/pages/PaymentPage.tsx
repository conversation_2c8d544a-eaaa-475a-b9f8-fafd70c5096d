import { useEffect, useState } from "react";
import { Link, useSearchParams } from "react-router-dom";
import { toast } from "sonner";
import { generatePaymentConfig } from "../services/api";
import SecurePayFields from "../components/payments/SecurePayFields";

interface PayFieldsConfig {
  merchantId: string;
  publicKey: string;
  amount: number;
  description: string;
  mode: "txn" | "txnToken" | "token";
  txnType: "sale" | "auth" | "ecsale";
}

const PaymentPage = () => {
  const [searchParams] = useSearchParams();
  const [payFieldsConfig, setPayFieldsConfig] = useState<PayFieldsConfig | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    // Get payment parameters from URL
    const merchantId = searchParams.get("merchantId");
    const description = searchParams.get("description");
    const amount = searchParams.get("amount");

    if (!merchantId || !description) {
      setError("Missing payment parameters. Please use a valid payment link.");
      setLoading(false);
      return;
    }

    const generateConfig = async () => {
      try {
        const response = await generatePaymentConfig({
          merchantId,
          description,
          amount: amount ? parseInt(amount) : 1000,
        });

        setPayFieldsConfig(response.config);
        setLoading(false);
      } catch (error) {
        setError(error instanceof Error ? error.message : "Failed to load payment configuration");
        setLoading(false);
      }
    };

    generateConfig();
  }, [searchParams]);

  const handlePaymentSuccess = () => {
    setSuccess(true);
    toast.success("Payment processed successfully!");
  };

  const handlePaymentFailure = (error: unknown) => {
    const errorMessage = error instanceof Error ? error.message : (error as { message?: string })?.message || "Payment processing failed";
    setError(errorMessage);
    toast.error(errorMessage);
  };

  if (error && !payFieldsConfig) {
    return (
      <div className="min-h-screen flex items-center justify-center px-6">
        <div className="text-center max-w-2xl mx-auto">
          <Link to="/" className="inline-block mb-8 text-slate-600 hover:text-slate-800 text-sm">
            ← Back to Home
          </Link>

          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <h1 className="text-2xl font-semibold text-red-800 mb-4">Payment Error</h1>
            <p className="text-red-700">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  if (success) {
    return (
      <div className="min-h-screen flex items-center justify-center px-6">
        <div className="text-center max-w-2xl mx-auto">
          <Link to="/" className="inline-block mb-8 text-slate-600 hover:text-slate-800 text-sm">
            ← Back to Home
          </Link>

          <div className="bg-green-50 border border-green-200 rounded-lg p-6">
            <h1 className="text-2xl font-semibold text-green-800 mb-4">Payment Successful!</h1>
            <p className="text-green-700">Your payment has been processed successfully.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-6">
      <div className="max-w-2xl mx-auto">
        <Link to="/" className="inline-block mb-8 text-slate-600 hover:text-slate-800 text-sm">
          ← Back to Home
        </Link>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <h1 className="text-2xl font-semibold text-slate-800 mb-6 text-center">Secure Payment</h1>

          {error && (
            <div className="mb-6 p-4 bg-red-50 text-red-700 rounded-lg border border-red-200">
              <p>{error}</p>
            </div>
          )}

          {payFieldsConfig ? (
            <SecurePayFields
              config={payFieldsConfig}
              onSuccess={handlePaymentSuccess}
              onFailure={handlePaymentFailure}
              className="max-w-md mx-auto"
            />
          ) : loading ? (
            <div className="text-center py-8">
              <div className="animate-spin h-8 w-8 border-4 border-t-transparent border-blue-600 rounded-full mx-auto"></div>
              <p className="mt-4 text-gray-600">Loading payment form...</p>
            </div>
          ) : null}
        </div>

        <div className="mt-6 text-center text-xs text-gray-500">
          <div className="flex items-center justify-center space-x-4">
            <span className="flex items-center">
              <svg className="w-4 h-4 mr-1 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fillRule="evenodd"
                  d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z"
                  clipRule="evenodd"
                />
              </svg>
              SSL Secured
            </span>
            <span className="flex items-center">
              <svg className="w-4 h-4 mr-1 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  clipRule="evenodd"
                />
              </svg>
              PCI Compliant
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentPage;
